import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Divider,
  Alert,
  CircularProgress,
  FormHelperText,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,

  Save as SaveIcon,
  ViewList as ViewListIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import parcoCaviService from '../../services/parcoCaviService';
import ConfigurazioneDialog from './ConfigurazioneDialog';
import BobineFilterableTable from './BobineFilterableTable';
import QuickAddCablesDialog from './QuickAddCablesDialog';
import { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';

const ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {
  const [loading, setLoading] = useState(false);
  const [bobine, setBobine] = useState([]);
  const [selectedOption, setSelectedOption] = useState(initialOption);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedBobina, setSelectedBobina] = useState(null);
  const [showQuickAddDialog, setShowQuickAddDialog] = useState(false);
  const [formData, setFormData] = useState({
    numero_bobina: '',
    utility: '',
    tipologia: '',
    n_conduttori: '0',  // Imposta sempre a '0' per il campo spare
    sezione: '',
    metri_totali: '',
    metri_residui: '',
    stato_bobina: 'Disponibile',
    ubicazione_bobina: '',
    fornitore: '',
    n_DDT: '',
    data_DDT: '',
    configurazione: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [formWarnings, setFormWarnings] = useState({});

  const [openConfigDialog, setOpenConfigDialog] = useState(false);
  const [isFirstInsertion, setIsFirstInsertion] = useState(false);

  // Carica le bobine disponibili
  const loadBobine = async () => {
    try {
      setLoading(true);
      const data = await parcoCaviService.getBobine(cantiereId);
      setBobine(data);
    } catch (error) {
      let errorMessage = 'Errore nel caricamento delle bobine';
      if (error.detail) {
        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);
      } else if (error.message) {
        errorMessage += ': ' + error.message;
      }
      onError(errorMessage);
      console.error('Errore nel caricamento delle bobine:', error);
    } finally {
      setLoading(false);
    }
  };

  // Effetto per popolare il form quando viene selezionata una bobina per la modifica
  useEffect(() => {
    if (selectedBobina && dialogType === 'modificaBobina') {
      console.log('Popolamento form per modifica bobina:', selectedBobina);

      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe
      setFormData({
        numero_bobina: selectedBobina.numero_bobina || '',
        utility: String(selectedBobina.utility || ''),
        tipologia: String(selectedBobina.tipologia || ''),
        n_conduttori: selectedBobina.n_conduttori !== null && selectedBobina.n_conduttori !== undefined ? String(selectedBobina.n_conduttori) : '',
        sezione: selectedBobina.sezione !== null && selectedBobina.sezione !== undefined ? String(selectedBobina.sezione) : '',
        metri_totali: selectedBobina.metri_totali !== null && selectedBobina.metri_totali !== undefined ? Number(selectedBobina.metri_totali) : '',
        metri_residui: selectedBobina.metri_residui !== null && selectedBobina.metri_residui !== undefined ? Number(selectedBobina.metri_residui) : '',
        stato_bobina: String(selectedBobina.stato_bobina || 'Disponibile'),
        ubicazione_bobina: String(selectedBobina.ubicazione_bobina || ''),
        fornitore: String(selectedBobina.fornitore || ''),
        n_DDT: String(selectedBobina.n_DDT || ''),
        data_DDT: selectedBobina.data_DDT || '',
        configurazione: String(selectedBobina.configurazione || 's')
      });

      console.log('Form data impostati per la modifica:', {
        numero_bobina: selectedBobina.numero_bobina,
        utility: selectedBobina.utility,
        tipologia: selectedBobina.tipologia,
        n_conduttori: selectedBobina.n_conduttori,
        sezione: selectedBobina.sezione,
        metri_totali: selectedBobina.metri_totali,
        metri_residui: selectedBobina.metri_residui
      });
    }
  }, [selectedBobina, dialogType]);



  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale
  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito
  const initialLoadDone = React.useRef(false);

  useEffect(() => {
    // Esegui solo una volta all'avvio del componente
    if (!initialLoadDone.current) {
      console.log('Primo caricamento del componente, initialOption:', initialOption);
      initialLoadDone.current = true;

      if (initialOption === 'creaBobina') {
        console.log('Avvio processo creazione bobina');
        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione
        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\'AVVIO');
        setIsFirstInsertion(true);
        setOpenConfigDialog(true);
      } else if (initialOption) {
        console.log('Eseguendo handleOptionSelect con:', initialOption);
        handleOptionSelect(initialOption);
      } else {
        console.log('Caricando bobine');
        loadBobine();
      }
    }
  }, []);  // Dipendenze vuote per eseguire solo al mount

  // Verifica se è il primo inserimento di una bobina per un cantiere
  const checkIfFirstInsertion = async () => {
    // Variabile per memorizzare la configurazione
    let configurazione = 's'; // Valore predefinito

    try {
      // Previene chiamate multiple
      if (loading) {
        console.log('Operazione già in corso, uscita');
        return;
      }

      // Assicuriamoci che nessun dialog sia aperto
      setOpenDialog(false);
      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto

      setLoading(true);
      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);

      // Gestione caso in cui cantiereId non sia valido
      if (!cantiereId || isNaN(parseInt(cantiereId))) {
        onError('ID cantiere non valido');
        console.error('ID cantiere non valido:', cantiereId);
        setLoading(false);
        return;
      }

      // Chiamata API reale
      let isFirst = false;

      try {
        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);
        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);
        isFirst = response.is_first_insertion;

        // Controlla se c'è una configurazione salvata in localStorage
        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);

        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server
        configurazione = savedConfig || response.configurazione || 's';

        console.log('Configurazione da localStorage:', savedConfig);
        console.log('Configurazione dal server:', response.configurazione);
        console.log('Configurazione finale utilizzata:', configurazione);

        setIsFirstInsertion(isFirst);
        console.log('È il primo inserimento di una bobina?', isFirst);
        console.log('Configurazione esistente:', configurazione);
      } catch (error) {
        console.error('Errore durante la verifica del primo inserimento:', error);
        // In caso di errore, assumiamo che non sia il primo inserimento
        setIsFirstInsertion(false);
        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');
      }

      if (isFirst) {
        // Se è il primo inserimento, mostra il dialog di configurazione
        console.log('Mostrando il dialog di configurazione');
        // Assicuriamoci che il dialog di creazione sia chiuso
        setOpenDialog(false);

        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione
        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');
        setOpenConfigDialog(true);
      } else {
        // Non è il primo inserimento, procedi con il form normale
        console.log('Non è il primo inserimento, mostrando il form normale');

        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica
        let nextBobinaNumber = '1';
        if (configurazione === 's') {
          try {
            // Ottieni l'ultimo numero di bobina dal backend
            const bobine = await parcoCaviService.getBobine(cantiereId);
            if (bobine && bobine.length > 0) {
              // Filtra solo le bobine con numero_bobina numerico
              const numericBobine = bobine.filter(b => b.numero_bobina && /^\d+$/.test(b.numero_bobina));

              // Log per debug
              console.log('Bobine totali:', bobine.length);
              console.log('Bobine con numero numerico:', numericBobine.length);
              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));

              if (numericBobine.length > 0) {
                // Trova il numero massimo tra le bobine esistenti
                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));
                console.log('Numero massimo trovato:', maxNumber);
                nextBobinaNumber = String(maxNumber + 1);
              }
            }
            console.log('Prossimo numero bobina:', nextBobinaNumber);
          } catch (error) {
            console.error('Errore nel recupero del prossimo numero bobina:', error);
            // In caso di errore, usa 1 come default
            nextBobinaNumber = '1';
          }
        }

        setDialogType('creaBobina');
        setFormData({
          // In modalità automatica, imposta il numero progressivo
          // In modalità manuale, lascia vuoto per far inserire all'utente
          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',
          utility: '',
          tipologia: '',
          n_conduttori: '0',  // Imposta sempre a '0' per il campo spare
          sezione: '',
          metri_totali: '',
          metri_residui: '',
          stato_bobina: 'Disponibile',
          ubicazione_bobina: '',
          fornitore: '',
          n_DDT: '',
          data_DDT: '',
          configurazione: configurazione  // Usa la configurazione esistente
        });
        setOpenDialog(true);
      }
    } catch (error) {
      // Gestione dettagliata dell'errore
      let errorMessage = 'Errore nel controllo dell\'inserimento della prima bobina';

      if (error.response) {
        // Errore di risposta dal server
        errorMessage += `: ${error.response.status} - ${error.response.data?.detail || 'Errore server'}`;
        console.error('Dettagli errore API:', error.response);
      } else if (error.request) {
        // Errore di rete (nessuna risposta ricevuta)
        errorMessage += ': Errore di connessione al server';
      } else {
        // Errore generico
        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;
      }

      onError(errorMessage);
      console.error('Errore completo:', error);

      // In caso di errore, mantieni la configurazione esistente o usa il default
      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale
      if (!configurazione) {
        configurazione = 's'; // Fallback al valore di default solo se non è già impostato
      }

      setDialogType('creaBobina');
      setFormData({
        numero_bobina: configurazione === 's' ? '1' : '',
        utility: '',
        tipologia: '',
        n_conduttori: '0',  // Imposta sempre a '0' per il campo spare
        sezione: '',
        metri_totali: '',
        metri_residui: '',
        stato_bobina: 'Disponibile',
        ubicazione_bobina: '',
        fornitore: '',
        n_DDT: '',
        data_DDT: '',
        configurazione: configurazione  // Usa la configurazione esistente o il default
      });
      setOpenDialog(true);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la conferma della configurazione
  const handleConfigConfirm = async (configValue) => {
    console.log('Configurazione selezionata:', configValue);
    // Salva la configurazione selezionata in localStorage per persistenza
    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);
    setLoading(true);

    try {
      // Ottieni il prossimo numero di bobina se la configurazione è automatica
      let nextBobinaNumber = '1';
      if (configValue === 's') {
        try {
          // Ottieni l'ultimo numero di bobina dal backend
          const bobine = await parcoCaviService.getBobine(cantiereId);
          if (bobine && bobine.length > 0) {
            // Filtra solo le bobine con numero_bobina numerico
            const numericBobine = bobine.filter(b => b.numero_bobina && /^\d+$/.test(b.numero_bobina));

            // Log per debug
            console.log('Bobine totali:', bobine.length);
            console.log('Bobine con numero numerico:', numericBobine.length);
            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));

            if (numericBobine.length > 0) {
              // Trova il numero massimo tra le bobine esistenti
              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));
              console.log('Numero massimo trovato:', maxNumber);
              nextBobinaNumber = String(maxNumber + 1);
            }
          }
          console.log('Prossimo numero bobina:', nextBobinaNumber);
        } catch (error) {
          console.error('Errore nel recupero del prossimo numero bobina:', error);
          // In caso di errore, usa 1 come default
          nextBobinaNumber = '1';
        }
      }

      // Imposta i valori di default per la bobina
      const defaultFormData = {
        // In modalità automatica, imposta il numero progressivo
        // In modalità manuale, lascia vuoto per far inserire all'utente
        numero_bobina: configValue === 's' ? nextBobinaNumber : '',
        utility: '',
        tipologia: '',
        n_conduttori: '0',  // Imposta sempre a '0' per il campo spare
        sezione: '',
        metri_totali: '',
        metri_residui: '',
        stato_bobina: 'Disponibile',
        ubicazione_bobina: '',
        fornitore: '',
        n_DDT: '',
        data_DDT: '',
        configurazione: configValue // Imposta la configurazione scelta
      };

      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);

      // Importante: prima prepara il form, poi chiudi il dialog di configurazione
      // e solo dopo apri il dialog di creazione
      setFormData(defaultFormData);
      setDialogType('creaBobina');

      // Chiudi il dialog di configurazione
      setOpenConfigDialog(false);

      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso
      // prima di aprire il dialog di creazione
      setTimeout(() => {
        setOpenDialog(true);
        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);
      }, 300);
    } catch (error) {
      console.error('Errore durante la preparazione del form:', error);
      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la selezione di un'opzione dal menu
  const handleOptionSelect = (option) => {
    setSelectedOption(option);

    if (option === 'visualizzaBobine') {
      loadBobine();
    } else if (option === 'creaBobina') {
      checkIfFirstInsertion();
    } else if (option === 'modificaBobina') {
      loadBobine();
      setDialogType('selezionaBobina');
      setOpenDialog(true);
    } else if (option === 'eliminaBobina') {
      loadBobine();
      setDialogType('eliminaBobina');
      setOpenDialog(true);

  };

  // Gestisce la chiusura del dialog
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedBobina(null);

    // Recupera la configurazione salvata per questo cantiere
    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';

    setFormData({
      numero_bobina: '',
      utility: '',
      tipologia: '',
      n_conduttori: '0',  // Imposta sempre a '0' per il campo spare
      sezione: '',
      metri_totali: '',
      metri_residui: '',
      stato_bobina: 'Disponibile',
      ubicazione_bobina: '',
      fornitore: '',
      n_DDT: '',
      data_DDT: '',
      configurazione: savedConfig // Mantieni la configurazione salvata
    });
    setFormErrors({});
    setFormWarnings({});
  };

  // Gestisce la selezione di una bobina
  const handleBobinaSelect = (bobina) => {
    console.log('Bobina selezionata:', bobina);
    setSelectedBobina(bobina);
    if (dialogType === 'selezionaBobina') {
      setDialogType('modificaBobina');
      // Il popolamento del form è ora gestito dall'useEffect
    }
  };

  // Gestisce il cambio dei valori nel form con validazione
  const handleFormChange = (e) => {
    const { name, value } = e.target;

    // Aggiorna il valore nel form
    setFormData({
      ...formData,
      [name]: value
    });

    // Valida il campo
    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {
      // Validazione speciale per numero_bobina quando configurazione è 'n'
      if (name === 'numero_bobina' && formData.configurazione === 'n') {
        const idResult = validateBobinaId(value);
        if (!idResult.valid) {
          setFormErrors(prev => ({
            ...prev,
            [name]: idResult.message
          }));
        } else {
          setFormErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[name];
            return newErrors;
          });
        }
        return;
      }

      const result = validateBobinaField(name, value);

      // Aggiorna gli errori
      if (!result.valid) {
        setFormErrors(prev => ({
          ...prev,
          [name]: result.message
        }));
      } else {
        setFormErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });

        // Aggiorna gli avvisi
        if (result.warning) {
          setFormWarnings(prev => ({
            ...prev,
            [name]: result.message
          }));
        } else {
          setFormWarnings(prev => {
            const newWarnings = { ...prev };
            delete newWarnings[name];
            return newWarnings;
          });
        }
      }
    }
  };

  // Gestisce il salvataggio del form
  const handleSave = async () => {
    try {
      // Validazione completa dei dati prima del salvataggio
      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {
        const validation = validateBobinaData(formData);

        if (!validation.isValid) {
          setFormErrors(validation.errors);
          setFormWarnings(validation.warnings);
          onError('Correggi gli errori nel form prima di salvare');
          return;
        }

        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale
        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {
          setFormErrors({
            ...formErrors,
            numero_bobina: 'L\'ID della bobina è obbligatorio'
          });
          onError('L\'ID della bobina è obbligatorio');
          return;
        }
      }

      setLoading(true);
      console.log('Salvataggio dati bobina in corso...');

      if (dialogType === 'creaBobina') {
        // Prepara i dati per la creazione della bobina
        const bobinaData = {
          ...formData,
          // Assicurati che tutti i campi siano nel formato corretto
          numero_bobina: String(formData.numero_bobina || ''),
          utility: String(formData.utility || ''),
          tipologia: String(formData.tipologia || ''),
          n_conduttori: String(formData.n_conduttori || ''),
          sezione: String(formData.sezione || ''),
          metri_totali: parseFloat(formData.metri_totali) || 0,
          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),
          fornitore: String(formData.fornitore || 'TBD'),
          n_DDT: String(formData.n_DDT || 'TBD'),
          data_DDT: formData.data_DDT || null,
          // Assicurati che la configurazione sia impostata correttamente
          configurazione: String(formData.configurazione || 's')
        };

        console.log('Dati bobina da inviare:', bobinaData);

        try {
          // Log dei dati che stiamo per inviare
          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));

          // Invia i dati al backend
          await parcoCaviService.createBobina(cantiereId, bobinaData);
          onSuccess('Bobina creata con successo');

          // Chiudi il dialog
          handleCloseDialog();

          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine
          setSelectedOption('visualizzaBobine');
          loadBobine();

          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine
          if (typeof window !== 'undefined') {
            // Usa un evento personalizzato per comunicare con il componente padre
            const event = new CustomEvent('redirectToVisualizzaBobine', { detail: { cantiereId } });
            window.dispatchEvent(event);
          }
        } catch (error) {
          console.error('Errore durante la creazione della bobina:', error);

          // Gestione dettagliata dell'errore
          let errorMessage = 'Errore durante la creazione della bobina';

          if (error.detail) {
            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;
          } else if (error.message) {
            errorMessage = error.message;
          }

          // Log dettagliato dell'errore
          console.error('Dettagli errore:', errorMessage);
          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));

          onError(errorMessage);
          setLoading(false);
          return;
        }
      } else if (dialogType === 'modificaBobina') {
        // Per la modifica, usa l'ID bobina completo o il numero bobina
        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;

        console.log('Modifica bobina con ID:', bobinaId);
        console.log('Dati da inviare:', formData);

        try {
          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);
          console.log('Risposta modifica bobina:', response);
          onSuccess('Bobina modificata con successo');
        } catch (error) {
          console.error('Errore durante la modifica della bobina:', error);
          let errorMessage = 'Errore durante la modifica della bobina';
          if (error.detail) {
            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;
          } else if (error.message) {
            errorMessage = error.message;
          }
          onError(errorMessage);
          setLoading(false);
          return;
        }
      } else if (dialogType === 'eliminaBobina') {
        // Per l'eliminazione, usa l'ID bobina completo
        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;
        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);

        // Verifica se è stata eliminata l'ultima bobina
        if (response.is_last_bobina) {
          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);
          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');
          // Non forziamo più l'apertura del dialog di creazione quando l'ultima bobina viene eliminata
          // L'utente può decidere se inserire una nuova bobina o lasciare il parco cavi vuoto
        } else {
          onSuccess('Bobina eliminata con successo');
        }
      }

      handleCloseDialog();
      loadBobine(); // Ricarica le bobine dopo l'operazione
    } catch (error) {
      let errorMessage = 'Errore durante l\'operazione';
      if (error.detail) {
        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);
      } else if (error.message) {
        errorMessage += ': ' + error.message;
      } else {
        errorMessage += ': Errore sconosciuto';
      }
      onError(errorMessage);
      console.error('Errore durante l\'operazione:', error);
    } finally {
      setLoading(false);
    }
  };

  // La visualizzazione delle bobine è ora gestita dal componente BobineFilterableTable
  const renderBobineCards = () => {
    return (
      <BobineFilterableTable
        bobine={bobine}
        loading={loading}
        onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}
        onEdit={(bobina) => {
          setSelectedBobina(bobina);
          setDialogType('modificaBobina');
          setOpenDialog(true);
        }}
        onDelete={(bobina) => {
          setSelectedBobina(bobina);
          setDialogType('eliminaBobina');
          setOpenDialog(true);
        }}

        onQuickAdd={(bobina) => {
          setSelectedBobina(bobina);
          setShowQuickAddDialog(true);
        }}
      />
    );
  };

  // Renderizza il dialog in base al tipo
  const renderDialog = () => {
    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}
          </DialogTitle>
          <DialogContent>
            {dialogType === 'modificaBobina' && (
              <Alert severity="info" sx={{ mb: 2, mt: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold">
                  Condizioni per la modifica:
                </Typography>
                <ul>
                  <li>La bobina deve essere nello stato "Disponibile"</li>
                  <li>La bobina non deve essere associata a nessun cavo</li>
                  <li>Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente</li>
                </ul>
                <Box sx={{ mt: 1 }}>
                  <Typography variant="body2">
                    <strong>Stato attuale:</strong> {selectedBobina?.stato_bobina || 'N/A'}
                  </Typography>
                </Box>
              </Alert>
            )}

            {Object.keys(formWarnings).length > 0 && (
              <Alert severity="warning" sx={{ mb: 2, mt: 1 }}>
                <Typography variant="subtitle2">Attenzione:</Typography>
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                  {Object.values(formWarnings).map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </Alert>
            )}

            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="numero_bobina"
                  label="ID Bobina"
                  fullWidth
                  variant="outlined"
                  value={formData.numero_bobina}
                  onChange={handleFormChange}
                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}
                  required
                  error={!!formErrors.numero_bobina}
                  InputProps={{
                    sx: {
                      bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',
                      fontWeight: 'bold',
                    }
                  }}
                  helperText={
                    formErrors.numero_bobina ||
                    (dialogType === 'creaBobina' && formData.configurazione === 's'
                      ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)`
                      : dialogType === 'creaBobina' && formData.configurazione === 'n'
                        ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}`
                        : '')
                  }
                  type={formData.configurazione === 's' ? "text" : "text"}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="utility"
                  label="Utility"
                  fullWidth
                  variant="outlined"
                  value={formData.utility}
                  onChange={handleFormChange}
                  required
                  error={!!formErrors.utility}
                  helperText={formErrors.utility || ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="tipologia"
                  label="Tipologia"
                  fullWidth
                  variant="outlined"
                  value={formData.tipologia}
                  onChange={handleFormChange}
                  required
                  error={!!formErrors.tipologia}
                  helperText={formErrors.tipologia || ''}
                />
              </Grid>
              {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}
              <Grid item xs={12} sm={6}>
                <TextField
                  name="sezione"
                  label="Formazione"
                  fullWidth
                  variant="outlined"
                  value={formData.sezione}
                  onChange={handleFormChange}
                  required
                  error={!!formErrors.sezione}
                  helperText={formErrors.sezione || ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="metri_totali"
                  label="Metri Totali"
                  type="number"
                  fullWidth
                  variant="outlined"
                  value={formData.metri_totali}
                  onChange={handleFormChange}
                  required
                  error={!!formErrors.metri_totali}
                  helperText={formErrors.metri_totali || ''}
                />
              </Grid>
              {dialogType === 'modificaBobina' && (
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="metri_residui"
                    label="Metri Residui"
                    type="number"
                    fullWidth
                    variant="outlined"
                    value={formData.metri_residui}
                    onChange={handleFormChange}
                    required
                    disabled={true}
                    helperText="I metri residui non possono essere modificati direttamente"
                  />
                </Grid>
              )}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="stato-bobina-label">Stato Bobina</InputLabel>
                  <Select
                    labelId="stato-bobina-label"
                    name="stato_bobina"
                    value={formData.stato_bobina}
                    label="Stato Bobina"
                    onChange={handleFormChange}
                    disabled={dialogType === 'creaBobina'}
                  >
                    <MenuItem value="Disponibile">Disponibile</MenuItem>
                    <MenuItem value="In uso">In uso</MenuItem>
                    <MenuItem value="Terminata">Terminata</MenuItem>
                    <MenuItem value="Danneggiata">Danneggiata</MenuItem>
                    <MenuItem value="Over">Over</MenuItem>
                  </Select>
                  {dialogType === 'creaBobina' && (
                    <FormHelperText>Per una nuova bobina, lo stato è sempre "Disponibile"</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="ubicazione_bobina"
                  label="Ubicazione Bobina"
                  fullWidth
                  variant="outlined"
                  value={formData.ubicazione_bobina}
                  onChange={handleFormChange}
                  error={!!formErrors.ubicazione_bobina}
                  helperText={formErrors.ubicazione_bobina || ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="fornitore"
                  label="Fornitore"
                  fullWidth
                  variant="outlined"
                  value={formData.fornitore}
                  onChange={handleFormChange}
                  error={!!formErrors.fornitore}
                  helperText={formErrors.fornitore || ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="n_DDT"
                  label="Numero DDT"
                  fullWidth
                  variant="outlined"
                  value={formData.n_DDT}
                  onChange={handleFormChange}
                  error={!!formErrors.n_DDT}
                  helperText={formErrors.n_DDT || ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="data_DDT"
                  label="Data DDT (YYYY-MM-DD)"
                  fullWidth
                  variant="outlined"
                  value={formData.data_DDT}
                  onChange={handleFormChange}
                  placeholder="YYYY-MM-DD"
                  error={!!formErrors.data_DDT}
                  helperText={formErrors.data_DDT || ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="configurazione"
                  label="Modalità Numerazione"
                  fullWidth
                  variant="outlined"
                  value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}
                  InputProps={{
                    readOnly: true,
                    sx: {
                      bgcolor: '#f5f5f5',
                    }
                  }}
                  helperText={
                    <Box sx={{ fontWeight: 'medium', color: formData.configurazione === 's' ? 'success.main' : 'info.main' }}>
                      {formData.configurazione === 's'
                        ? 'Numerazione progressiva automatica (1, 2, 3, ...)'
                        : 'Inserimento manuale dell\'ID bobina (es. A123, SPEC01, ...)'}
                    </Box>
                  }
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleSave}
              disabled={loading || Object.keys(formErrors).length > 0}
              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
              variant="contained"
              color="primary"
            >
              Salva
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'selezionaBobina') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>
          <DialogContent>
            {loading ? (
              <CircularProgress />
            ) : bobine.length === 0 ? (
              <Alert severity="info">Nessuna bobina disponibile</Alert>
            ) : (
              <List>
                {bobine.map((bobina) => (
                  <ListItem
                    button
                    key={bobina.numero_bobina}
                    onClick={() => handleBobinaSelect(bobina)}
                  >
                    <ListItemText
                      primary={`Bobina: ${bobina.numero_bobina}`}
                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'eliminaBobina') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Elimina Bobina</DialogTitle>
          <DialogContent>
            {!selectedBobina ? (
              loading ? (
                <CircularProgress />
              ) : bobine.length === 0 ? (
                <Alert severity="info">Nessuna bobina disponibile</Alert>
              ) : (
                <List>
                  {bobine.map((bobina) => (
                    <ListItem
                      button
                      key={bobina.numero_bobina}
                      onClick={() => setSelectedBobina(bobina)}
                    >
                      <ListItemText
                        primary={`Bobina: ${bobina.numero_bobina}`}
                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}
                      />
                    </ListItem>
                  ))}
                </List>
              )
            ) : (
              <Box>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?
                </Alert>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  Questa operazione non può essere annullata.
                </Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Condizioni per l'eliminazione:
                  </Typography>
                  <ul>
                    <li>La bobina deve essere completamente integra (metri residui = metri totali)</li>
                    <li>La bobina deve essere nello stato "Disponibile"</li>
                    <li>La bobina non deve essere associata a nessun cavo</li>
                  </ul>
                </Alert>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Stato attuale:</strong> {selectedBobina.stato_bobina || 'N/A'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Metri totali:</strong> {selectedBobina.metri_totali || 'N/A'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Metri residui:</strong> {selectedBobina.metri_residui || 'N/A'}
                  </Typography>
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            {selectedBobina && (
              <Button
                onClick={handleSave}
                disabled={loading}
                color="error"
                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}
              >
                Elimina
              </Button>
            )}
          </DialogActions>
        </Dialog>
      );

    }

    return null;
  };

  return (
    <Box>
      {selectedOption === 'visualizzaBobine' && !openDialog ? (
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>
            <Box sx={{ display: 'flex', gap: 2 }}>

              <Button
                variant="contained"
                color="inherit"
                startIcon={<AddIcon />}
                onClick={() => checkIfFirstInsertion()}
                sx={{
                  fontWeight: 'medium',
                  borderRadius: 0
                }}
              >
                Crea Nuova Bobina
              </Button>
            </Box>
          </Box>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <BobineFilterableTable
              bobine={bobine}
              loading={loading}
              onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}
              onEdit={(bobina) => {
                setSelectedBobina(bobina);
                setDialogType('modificaBobina');
                setOpenDialog(true);
              }}
              onDelete={(bobina) => {
                setSelectedBobina(bobina);
                setDialogType('eliminaBobina');
                setOpenDialog(true);
              }}

              onQuickAdd={(bobina) => {
                setSelectedBobina(bobina);
                setShowQuickAddDialog(true);
              }}
            />
          )}
        </Paper>
      ) : !openDialog ? (
        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {!selectedOption ? (
            <Typography variant="body1">
              Seleziona un'opzione dal menu principale per iniziare.
            </Typography>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}
                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}
                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}
              </Typography>
              <CircularProgress sx={{ mt: 2 }} />
            </Box>
          )}
        </Paper>
      ) : null}

      {renderDialog()}

      {/* Dialog di configurazione per il primo inserimento */}
      <ConfigurazioneDialog
        open={openConfigDialog}
        onClose={() => setOpenConfigDialog(false)}
        onConfirm={handleConfigConfirm}
      />

      {/* Dialog per aggiungere rapidamente cavi a una bobina */}
      <QuickAddCablesDialog
        open={showQuickAddDialog}
        onClose={() => {
          setShowQuickAddDialog(false);
          // Ricarica le bobine per riflettere i cambiamenti
          loadBobine();
        }}
        bobina={selectedBobina}
        cantiereId={cantiereId}
        onSuccess={onSuccess}
        onError={onError}
      />
    </Box>
  );
};

export default ParcoCavi;
