import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CssBaseline } from '@mui/material';


import TopNavbar from '../components/TopNavbar';
import HomePage from './HomePage';
import AdminPage from './AdminPage';
import UserPage from './UserPage';
import CaviPage from './CaviPage';
import UserExpirationChecker from '../components/admin/UserExpirationChecker';

// Importa le nuove pagine per i cavi
import VisualizzaCaviPage from './cavi/VisualizzaCaviPage';
import ParcoCaviPage from './cavi/ParcoCaviPage';

import ReportCaviPageNew from './cavi/ReportCaviPageNew';
import CertificazioneCaviPage from './cavi/CertificazioneCaviPage';
import CertificazioneCEI64_8Page from './cavi/CertificazioneCEI64_8Page';
import CertificazioniPageDebug from './CertificazioniPageDebug';
import GestioneComandeePage from './cavi/GestioneComandeePage';
import TestCaviPage from './cavi/TestCaviPage';
import TestBobinePage from './TestBobinePage';

// Importa le pagine per Parco Cavi
import VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';
import CreaBobinaPage from './cavi/parco/CreaBobinaPage';
import ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';
import EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';
import StoricoUtilizzoPage from './cavi/parco/StoricoUtilizzoPage';

// Importa le pagine per Posa e Collegamenti - OBSOLETE: Funzionalità migrate ai popup
// import InserisciMetriPage from './cavi/posa/InserisciMetriPage';
// import MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';
// import PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';


// Importa la pagina per il cantiere specifico
import CantierePage from './cantieri/CantierePage';

// Importa le pagine per le comande
import ComandePage from './comande/ComandePage';
import TestComande from '../components/comande/TestComande';
import AccessoRapidoComanda from '../components/comande/AccessoRapidoComanda';

const Dashboard = () => {

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
      {/* Componente invisibile che verifica gli utenti scaduti */}
      <UserExpirationChecker />
      <CssBaseline />
      <TopNavbar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: '100%',
          backgroundColor: '#f5f5f5', // Sfondo grigio chiaro per l'area principale
          minHeight: 'calc(100vh - 40px)', // Altezza minima per coprire l'intera viewport meno l'altezza della navbar
          overflowX: 'hidden', // Previene scrollbar orizzontale
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/admin" element={<AdminPage />} />
            <Route path="/cantieri" element={<UserPage />} />
            <Route path="/cantieri/:cantiereId" element={<CantierePage />} />
            <Route path="/cantieri/:cantiereId/certificazioni" element={<CertificazioniPageDebug />} />

            {/* Route per la gestione cavi */}
            <Route path="/cavi" element={<CaviPage />} />
            <Route path="/cavi/visualizza" element={<VisualizzaCaviPage />} />
            <Route path="/cavi/posa" element={<Navigate to="/dashboard/cavi/visualizza" replace />} />
            <Route path="/cavi/parco" element={<ParcoCaviPage />} />
            <Route path="/cavi/excel" element={<Navigate to="/dashboard/cavi/visualizza" replace />} />
            <Route path="/cavi/report" element={<ReportCaviPageNew />} />
            <Route path="/cavi/:cantiereId/report" element={<ReportCaviPageNew />} />

            <Route path="/cavi/certificazione" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/visualizza" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/filtra" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/crea" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/modifica" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/dettagli" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/pdf" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/elimina" element={<CertificazioneCaviPage />} />
            <Route path="/cavi/certificazione/strumenti" element={<CertificazioneCaviPage />} />

            {/* Route per Certificazione CEI 64-8 */}
            <Route path="/cavi/certificazione-cei/dashboard" element={<CertificazioneCEI64_8Page />} />
            <Route path="/cavi/certificazione-cei/rapporti" element={<CertificazioneCEI64_8Page />} />
            <Route path="/cavi/certificazione-cei/prove" element={<CertificazioneCEI64_8Page />} />
            <Route path="/cavi/certificazione-cei/non-conformita" element={<CertificazioneCEI64_8Page />} />
            <Route path="/cavi/certificazione-cei/strumenti" element={<CertificazioneCEI64_8Page />} />

            <Route path="/cavi/comande" element={<GestioneComandeePage />} />
            <Route path="/cantieri/:cantiereId/comande" element={<ComandePage />} />
            <Route path="/test/comande" element={<TestComande />} />
            <Route path="/accesso-rapido-comanda" element={<AccessoRapidoComanda />} />
            <Route path="/cavi/test" element={<TestCaviPage />} />

            {/* Route per la pagina di test delle bobine */}
            <Route path="/cavi/test-bobine/:cantiereId" element={<TestBobinePage />} />

            {/* Route per Parco Cavi */}
            <Route path="/cavi/parco/visualizza" element={<VisualizzaBobinePage />} />
            <Route path="/cavi/parco/crea" element={<CreaBobinaPage />} />
            <Route path="/cavi/parco/modifica" element={<ParcoCaviModificaBobinaPage />} />
            <Route path="/cavi/parco/elimina" element={<EliminaBobinaPage />} />
            <Route path="/cavi/parco/storico" element={<StoricoUtilizzoPage />} />

            {/* Route per Posa e Collegamenti - OBSOLETE: Funzionalità migrate ai popup */}
            {/* <Route path="/cavi/posa/inserisci-metri" element={<InserisciMetriPage />} /> */}
            {/* <Route path="/cavi/posa/metri-posati-semplificato" element={<MetriPosatiSemplificatoPage />} /> */}
            {/* <Route path="/cavi/posa/modifica-bobina" element={<PosaCaviModificaBobinaPage />} /> */}
            {/* <Route path="/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?" element={<PosaCaviModificaBobinaPage />} /> */}

            {/* Altre route verranno aggiunte man mano che vengono implementate */}
          </Routes>
      </Box>
    </Box>
  );
};

export default Dashboard;
